/* Student Portal Specific Styles */

/* Portal Header */
.portal-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  box-shadow: var(--shadow-lg);
}

.portal-header .logo h1 {
  color: var(--white);
  margin-bottom: var(--spacing-1);
}

.portal-type {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.portal-nav {
  display: flex;
  gap: var(--spacing-6);
}

.portal-nav .nav-link {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.portal-nav .nav-link:hover,
.portal-nav .nav-link.active {
  color: var(--white);
  background-color: rgba(255, 255, 255, 0.1);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-avatar img {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-name {
  font-weight: 500;
  color: var(--white);
}

.logout-btn {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Main Content */
.main-content {
  padding: var(--spacing-8) 0;
  min-height: calc(100vh - 80px);
}

/* Welcome Section */
.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-6);
  background: linear-gradient(135deg, var(--white), var(--gray-50));
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.welcome-content h2 {
  font-size: var(--font-size-3xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.welcome-content p {
  color: var(--gray-600);
  font-size: var(--font-size-lg);
  margin-bottom: 0;
}

.quick-stats {
  display: flex;
  gap: var(--spacing-4);
}

.stat-card {
  text-align: center;
  padding: var(--spacing-4);
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  min-width: 100px;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-6);
}

.dashboard-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.dashboard-card h3 {
  font-size: var(--font-size-xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-4);
  border-bottom: 2px solid var(--gray-100);
  padding-bottom: var(--spacing-2);
}

/* Current Lesson Card */
.lesson-preview {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.lesson-image {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-lg);
  object-fit: cover;
}

.lesson-info {
  flex: 1;
}

.lesson-info h4 {
  font-size: var(--font-size-lg);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.lesson-info p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-3);
}

.lesson-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
}

.lesson-progress span {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: 500;
}

/* Upcoming Class Card */
.class-info {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.class-time {
  text-align: center;
  padding: var(--spacing-3);
  background: var(--primary-color);
  color: var(--white);
  border-radius: var(--radius-lg);
  min-width: 100px;
}

.time {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin-bottom: var(--spacing-1);
}

.date {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.class-details h4 {
  font-size: var(--font-size-lg);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.class-details p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-sm);
}

/* AI Tutor Card */
.ai-preview {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.ai-avatar img {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  border: 3px solid var(--accent-color);
}

.ai-message {
  flex: 1;
  background: var(--gray-50);
  padding: var(--spacing-3);
  border-radius: var(--radius-lg);
  position: relative;
}

.ai-message::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid var(--gray-50);
}

.ai-message p {
  margin: 0;
  color: var(--gray-700);
  font-style: italic;
}

/* Recent Progress Card */
.achievements-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.achievement {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.achievement-icon {
  font-size: var(--font-size-xl);
  width: 40px;
  text-align: center;
}

.achievement-text h4 {
  font-size: var(--font-size-base);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.achievement-text p {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin: 0;
}

/* Quick Actions Card */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  background: var(--gray-50);
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  color: var(--gray-700);
}

.action-btn:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

.action-icon {
  font-size: var(--font-size-xl);
}

.action-btn span:last-child {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* Study Streak Card */
.streak-display {
  text-align: center;
  margin-bottom: var(--spacing-4);
}

.streak-number {
  font-size: var(--font-size-5xl);
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
}

.streak-label {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  margin-top: var(--spacing-1);
}

.study-streak p {
  text-align: center;
  color: var(--gray-600);
  margin-bottom: var(--spacing-4);
}

.streak-calendar {
  display: flex;
  justify-content: center;
  gap: var(--spacing-2);
}

.day {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: 600;
  background: var(--gray-200);
  color: var(--gray-600);
}

.day.completed {
  background: var(--secondary-color);
  color: var(--white);
}

/* Button Styles */
.btn-accent {
  background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  color: var(--white);
}

.btn-accent:hover {
  background: linear-gradient(135deg, var(--accent-light), var(--accent-color));
  color: var(--white);
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-4);
  }
  
  .quick-stats {
    justify-content: center;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .portal-nav {
    display: none;
  }
  
  .user-menu {
    gap: var(--spacing-2);
  }
  
  .user-name {
    display: none;
  }
}

@media (max-width: 480px) {
  .lesson-preview,
  .class-info,
  .ai-preview {
    flex-direction: column;
    text-align: center;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
